# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: BackendSecurityPolicy
metadata:
  name: envoy-ai-gateway-basic-gcp-credentials
  namespace: default
spec:
  type: GCPCredentials
  gcpCredentials:
    projectName: GCP_PROJECT_NAME
    region: GCP_REGION
    workloadIdentityFederationConfig:
      projectID: GCP_PROJECT_ID
      workloadIdentityPoolName: GCP_WORKLOAD_IDENTITY_POOL
      workloadIdentityProviderName: GCP_IDENTITY_PROVIDER_NAME
      serviceAccountImpersonation:
        serviceAccountName: SERVICE_ACCOUNT_NAME
      oidcExchangeToken:
        oidc:
          provider:
            issuer: GCP_OIDC_PROVIDER_ISSUER
          clientID: GCP_OIDC_CLIENT_ID
          clientSecret:
            name: envoy-ai-gateway-basic-gcp-client-secret
            namespace: default
