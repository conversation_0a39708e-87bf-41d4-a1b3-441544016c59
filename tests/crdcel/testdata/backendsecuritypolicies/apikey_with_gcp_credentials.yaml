# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: BackendSecurityPolicy
metadata:
  name: apikey-with-gcp-policy
  namespace: default
spec:
  type: APIKey
  gcpCredentials:
    projectName: test-project
    region: test-gcp-region
    workloadIdentityFederationConfig:
      projectID: test-project
      workloadIdentityPoolName: test-pool
      workloadIdentityProviderName: test-provider
      serviceAccountImpersonation:
        serviceAccountName: SERVICE_ACCOUNT_NAME
      oidcExchangeToken:
        oidc:
          provider:
            issuer: https://test-issuer.com
          clientID: test-client-id
          clientSecret:
            name: gcp-client-secret
