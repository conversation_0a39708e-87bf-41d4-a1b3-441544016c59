---
version: 2
interactions:
    - id: 0
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 428
        host: api.openai.com
        body: |-
            {
              "messages": [
                {
                  "content": [
                    {
                      "text": "What's in this image?",
                      "type": "text"
                    },
                    {
                      "image_url": {
                        "url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
                      },
                      "type": "image_url"
                    }
                  ],
                  "role": "user"
                }
              ],
              "model": "gpt-4.1-nano"
            }
        headers:
            Accept-Encoding:
                - gzip
            Content-Length:
                - "274"
            Content-Type:
                - application/json
            User-Agent:
                - Go-http-client/1.1
        url: https://api.openai.com/v1/chat/completions
        method: POST
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        content_length: 877
        body: |-
            {
              "choices": [
                {
                  "finish_reason": "stop",
                  "index": 0,
                  "logprobs": null,
                  "message": {
                    "annotations": [],
                    "content": "It seems the image didn't load properly. Could you please try uploading it again?",
                    "refusal": null,
                    "role": "assistant"
                  }
                }
              ],
              "created": 1753162062,
              "id": "chatcmpl-BvzpObAOzjs99gvHvWxmnRoOx9tP3",
              "model": "gpt-4.1-nano-2025-04-14",
              "object": "chat.completion",
              "service_tier": "default",
              "system_fingerprint": null,
              "usage": {
                "completion_tokens": 16,
                "completion_tokens_details": {
                  "accepted_prediction_tokens": 0,
                  "audio_tokens": 0,
                  "reasoning_tokens": 0,
                  "rejected_prediction_tokens": 0
                },
                "prompt_tokens": 16,
                "prompt_tokens_details": {
                  "audio_tokens": 0,
                  "cached_tokens": 0
                },
                "total_tokens": 32
              }
            }
        headers:
            Access-Control-Expose-Headers:
                - X-Request-ID
            Alt-Svc:
                - h3=":443"; ma=86400
            Cf-Cache-Status:
                - DYNAMIC
            Cf-Ray:
                - 963087c92c33e580-KUL
            Content-Type:
                - application/json
            Date:
                - Tue, 22 Jul 2025 05:27:43 GMT
            Openai-Processing-Ms:
                - "528"
            Openai-Project:
                - proj_KYenqYOfeZsnXEVK8dXVBhez
            Openai-Version:
                - "2020-10-01"
            Server:
                - cloudflare
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains; preload
            X-Content-Type-Options:
                - nosniff
            X-Envoy-Upstream-Service-Time:
                - "512"
            X-Ratelimit-Limit-Input-Images:
                - "50000"
            X-Ratelimit-Limit-Requests:
                - "500"
            X-Ratelimit-Limit-Tokens:
                - "200000"
            X-Ratelimit-Remaining-Input-Images:
                - "49999"
            X-Ratelimit-Remaining-Requests:
                - "499"
            X-Ratelimit-Remaining-Tokens:
                - "199227"
            X-Ratelimit-Reset-Input-Images:
                - 1ms
            X-Ratelimit-Reset-Requests:
                - 120ms
            X-Ratelimit-Reset-Tokens:
                - 231ms
            X-Request-Id:
                - req_3837ed73f5b1dd7805411b52bd36065e
        status: 200 OK
        code: 200
        duration: 878.325208ms
