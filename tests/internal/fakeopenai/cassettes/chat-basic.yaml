---
version: 2
interactions:
    - id: 0
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 110
        host: api.openai.com
        body: |-
            {
              "messages": [
                {
                  "content": "Hello!",
                  "role": "user"
                }
              ],
              "model": "gpt-4.1-nano"
            }
        headers:
            Accept-Encoding:
                - gzip
            Content-Length:
                - "72"
            Content-Type:
                - application/json
            User-Agent:
                - Go-http-client/1.1
        url: https://api.openai.com/v1/chat/completions
        method: POST
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        content_length: 828
        body: |-
            {
              "choices": [
                {
                  "finish_reason": "stop",
                  "index": 0,
                  "logprobs": null,
                  "message": {
                    "annotations": [],
                    "content": "Hello! How can I assist you today?",
                    "refusal": null,
                    "role": "assistant"
                  }
                }
              ],
              "created": 1753162006,
              "id": "chatcmpl-BvzoUDLRFA18TkSzKSxeGRL5XpbRr",
              "model": "gpt-4.1-nano-2025-04-14",
              "object": "chat.completion",
              "service_tier": "default",
              "system_fingerprint": null,
              "usage": {
                "completion_tokens": 9,
                "completion_tokens_details": {
                  "accepted_prediction_tokens": 0,
                  "audio_tokens": 0,
                  "reasoning_tokens": 0,
                  "rejected_prediction_tokens": 0
                },
                "prompt_tokens": 9,
                "prompt_tokens_details": {
                  "audio_tokens": 0,
                  "cached_tokens": 0
                },
                "total_tokens": 18
              }
            }
        headers:
            Access-Control-Expose-Headers:
                - X-Request-ID
            Alt-Svc:
                - h3=":443"; ma=86400
            Cf-Cache-Status:
                - DYNAMIC
            Cf-Ray:
                - 963086698d4ee580-KUL
            Content-Type:
                - application/json
            Date:
                - Tue, 22 Jul 2025 05:26:46 GMT
            Openai-Processing-Ms:
                - "426"
            Openai-Project:
                - proj_KYenqYOfeZsnXEVK8dXVBhez
            Openai-Version:
                - "2020-10-01"
            Server:
                - cloudflare
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains; preload
            X-Content-Type-Options:
                - nosniff
            X-Envoy-Upstream-Service-Time:
                - "451"
            X-Ratelimit-Limit-Requests:
                - "500"
            X-Ratelimit-Limit-Tokens:
                - "200000"
            X-Ratelimit-Remaining-Requests:
                - "499"
            X-Ratelimit-Remaining-Tokens:
                - "199996"
            X-Ratelimit-Reset-Requests:
                - 120ms
            X-Ratelimit-Reset-Tokens:
                - 1ms
            X-Request-Id:
                - req_85aa693b052b550db051da27ddf9bc5b
        status: 200 OK
        code: 200
        duration: 974.808375ms
