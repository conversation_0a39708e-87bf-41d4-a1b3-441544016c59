---
version: 2
interactions:
    - id: 0
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 148
        host: api.openai.com
        body: |-
            {
              "max_tokens": 0,
              "messages": [
                {
                  "content": null,
                  "role": "user"
                }
              ],
              "model": "gpt-4.1-nano",
              "temperature": -0.5
            }
        headers:
            Accept-Encoding:
                - gzip
            Content-Length:
                - "102"
            Content-Type:
                - application/json
            User-Agent:
                - Go-http-client/1.1
        url: https://api.openai.com/v1/chat/completions
        method: POST
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        content_length: 233
        body: |-
            {
              "error": {
                "code": "integer_below_min_value",
                "message": "Invalid 'max_tokens': integer below minimum value. Expected a value >= 1, but got 0 instead.",
                "param": "max_tokens",
                "type": "invalid_request_error"
              }
            }
        headers:
            Access-Control-Expose-Headers:
                - X-Request-ID
            Alt-Svc:
                - h3=":443"; ma=86400
            Cf-Cache-Status:
                - DYNAMIC
            Cf-Ray:
                - 963087c4caf0e580-KUL
            Content-Length:
                - "233"
            Content-Type:
                - application/json
            Date:
                - Tue, 22 Jul 2025 05:27:41 GMT
            Openai-Processing-Ms:
                - "11"
            Openai-Project:
                - proj_KYenqYOfeZsnXEVK8dXVBhez
            Openai-Version:
                - "2020-10-01"
            Server:
                - cloudflare
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains; preload
            X-Content-Type-Options:
                - nosniff
            X-Envoy-Upstream-Service-Time:
                - "14"
            X-Ratelimit-Limit-Requests:
                - "500"
            X-Ratelimit-Limit-Tokens:
                - "200000"
            X-Ratelimit-Remaining-Requests:
                - "499"
            X-Ratelimit-Remaining-Tokens:
                - "199997"
            X-Ratelimit-Reset-Requests:
                - 120ms
            X-Ratelimit-Reset-Tokens:
                - 0s
            X-Request-Id:
                - req_6b0e241346cd940cb87f851150345ea2
        status: 400 Bad Request
        code: 400
        duration: 347.307208ms
