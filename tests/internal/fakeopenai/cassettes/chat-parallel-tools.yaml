---
version: 2
interactions:
    - id: 0
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 866
        host: api.openai.com
        body: |-
            {
              "messages": [
                {
                  "content": "What is the weather like in San Francisco?",
                  "role": "user"
                }
              ],
              "model": "gpt-4.1-nano",
              "parallel_tool_calls": true,
              "tool_choice": "auto",
              "tools": [
                {
                  "function": {
                    "description": "Get the current weather in a given location",
                    "name": "get_current_weather",
                    "parameters": {
                      "properties": {
                        "location": {
                          "description": "The city and state, e.g. San Francisco, CA",
                          "type": "string"
                        },
                        "unit": {
                          "enum": [
                            "celsius",
                            "fahrenheit"
                          ],
                          "type": "string"
                        }
                      },
                      "required": [
                        "location"
                      ],
                      "type": "object"
                    }
                  },
                  "type": "function"
                }
              ]
            }
        headers:
            Accept-Encoding:
                - gzip
            Content-Length:
                - "503"
            Content-Type:
                - application/json
            User-Agent:
                - Go-http-client/1.1
        url: https://api.openai.com/v1/chat/completions
        method: POST
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        content_length: 1359
        body: |-
            {
              "choices": [
                {
                  "finish_reason": "tool_calls",
                  "index": 0,
                  "logprobs": null,
                  "message": {
                    "annotations": [],
                    "content": null,
                    "refusal": null,
                    "role": "assistant",
                    "tool_calls": [
                      {
                        "function": {
                          "arguments": "{\"location\": \"San Francisco, CA\"}",
                          "name": "get_current_weather"
                        },
                        "id": "call_D6u1WUoe5SU9K153FitUAhfw",
                        "type": "function"
                      },
                      {
                        "function": {
                          "arguments": "{\"location\": \"San Francisco, CA\"}",
                          "name": "get_current_weather"
                        },
                        "id": "call_aQlRzPlzkHJ29YluNaXlVfuT",
                        "type": "function"
                      }
                    ]
                  }
                }
              ],
              "created": 1753162059,
              "id": "chatcmpl-BvzpL1xt8QNyiT9xFDZp0olg9pSEx",
              "model": "gpt-4.1-nano-2025-04-14",
              "object": "chat.completion",
              "service_tier": "default",
              "system_fingerprint": null,
              "usage": {
                "completion_tokens": 52,
                "completion_tokens_details": {
                  "accepted_prediction_tokens": 0,
                  "audio_tokens": 0,
                  "reasoning_tokens": 0,
                  "rejected_prediction_tokens": 0
                },
                "prompt_tokens": 81,
                "prompt_tokens_details": {
                  "audio_tokens": 0,
                  "cached_tokens": 0
                },
                "total_tokens": 133
              }
            }
        headers:
            Access-Control-Expose-Headers:
                - X-Request-ID
            Alt-Svc:
                - h3=":443"; ma=86400
            Cf-Cache-Status:
                - DYNAMIC
            Cf-Ray:
                - 963087b95b90e580-KUL
            Content-Type:
                - application/json
            Date:
                - Tue, 22 Jul 2025 05:27:40 GMT
            Openai-Processing-Ms:
                - "613"
            Openai-Project:
                - proj_KYenqYOfeZsnXEVK8dXVBhez
            Openai-Version:
                - "2020-10-01"
            Server:
                - cloudflare
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains; preload
            X-Content-Type-Options:
                - nosniff
            X-Envoy-Upstream-Service-Time:
                - "620"
            X-Ratelimit-Limit-Requests:
                - "500"
            X-Ratelimit-Limit-Tokens:
                - "200000"
            X-Ratelimit-Remaining-Requests:
                - "499"
            X-Ratelimit-Remaining-Tokens:
                - "199987"
            X-Ratelimit-Reset-Requests:
                - 120ms
            X-Ratelimit-Reset-Tokens:
                - 3ms
            X-Request-Id:
                - req_ce34af16c966b5df11e0b240f401058f
        status: 200 OK
        code: 200
        duration: 910.667292ms
