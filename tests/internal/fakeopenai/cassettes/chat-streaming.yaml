---
version: 2
interactions:
    - id: 0
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 269
        host: api.openai.com
        body: |-
            {
              "messages": [
                {
                  "content": "You are a helpful assistant.",
                  "role": "developer"
                },
                {
                  "content": "Hello!",
                  "role": "user"
                }
              ],
              "model": "gpt-4.1-nano",
              "stream": true,
              "stream_options": {
                "include_usage": true
              }
            }
        headers:
            Accept-Encoding:
                - gzip
            Content-Length:
                - "188"
            Content-Type:
                - application/json
            User-Agent:
                - Go-http-client/1.1
        url: https://api.openai.com/v1/chat/completions
        method: POST
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        content_length: 3740
        body: |+
            data: {"id":"chatcmpl-BvzoWhZJbwLOqU99BTljeM8ve29kc","object":"chat.completion.chunk","created":1753162008,"model":"gpt-4.1-nano-2025-04-14","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{"role":"assistant","content":"","refusal":null},"logprobs":null,"finish_reason":null}],"usage":null}

            data: {"id":"chatcmpl-BvzoWhZJbwLOqU99BTljeM8ve29kc","object":"chat.completion.chunk","created":1753162008,"model":"gpt-4.1-nano-2025-04-14","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{"content":"Hello"},"logprobs":null,"finish_reason":null}],"usage":null}

            data: {"id":"chatcmpl-BvzoWhZJbwLOqU99BTljeM8ve29kc","object":"chat.completion.chunk","created":1753162008,"model":"gpt-4.1-nano-2025-04-14","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{"content":"!"},"logprobs":null,"finish_reason":null}],"usage":null}

            data: {"id":"chatcmpl-BvzoWhZJbwLOqU99BTljeM8ve29kc","object":"chat.completion.chunk","created":1753162008,"model":"gpt-4.1-nano-2025-04-14","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{"content":" How"},"logprobs":null,"finish_reason":null}],"usage":null}

            data: {"id":"chatcmpl-BvzoWhZJbwLOqU99BTljeM8ve29kc","object":"chat.completion.chunk","created":1753162008,"model":"gpt-4.1-nano-2025-04-14","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{"content":" can"},"logprobs":null,"finish_reason":null}],"usage":null}

            data: {"id":"chatcmpl-BvzoWhZJbwLOqU99BTljeM8ve29kc","object":"chat.completion.chunk","created":1753162008,"model":"gpt-4.1-nano-2025-04-14","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{"content":" I"},"logprobs":null,"finish_reason":null}],"usage":null}

            data: {"id":"chatcmpl-BvzoWhZJbwLOqU99BTljeM8ve29kc","object":"chat.completion.chunk","created":1753162008,"model":"gpt-4.1-nano-2025-04-14","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{"content":" assist"},"logprobs":null,"finish_reason":null}],"usage":null}

            data: {"id":"chatcmpl-BvzoWhZJbwLOqU99BTljeM8ve29kc","object":"chat.completion.chunk","created":1753162008,"model":"gpt-4.1-nano-2025-04-14","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{"content":" you"},"logprobs":null,"finish_reason":null}],"usage":null}

            data: {"id":"chatcmpl-BvzoWhZJbwLOqU99BTljeM8ve29kc","object":"chat.completion.chunk","created":1753162008,"model":"gpt-4.1-nano-2025-04-14","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{"content":" today"},"logprobs":null,"finish_reason":null}],"usage":null}

            data: {"id":"chatcmpl-BvzoWhZJbwLOqU99BTljeM8ve29kc","object":"chat.completion.chunk","created":1753162008,"model":"gpt-4.1-nano-2025-04-14","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{"content":"?"},"logprobs":null,"finish_reason":null}],"usage":null}

            data: {"id":"chatcmpl-BvzoWhZJbwLOqU99BTljeM8ve29kc","object":"chat.completion.chunk","created":1753162008,"model":"gpt-4.1-nano-2025-04-14","service_tier":"default","system_fingerprint":null,"choices":[{"index":0,"delta":{},"logprobs":null,"finish_reason":"stop"}],"usage":null}

            data: {"id":"chatcmpl-BvzoWhZJbwLOqU99BTljeM8ve29kc","object":"chat.completion.chunk","created":1753162008,"model":"gpt-4.1-nano-2025-04-14","service_tier":"default","system_fingerprint":null,"choices":[],"usage":{"prompt_tokens":19,"completion_tokens":9,"total_tokens":28,"prompt_tokens_details":{"cached_tokens":0,"audio_tokens":0},"completion_tokens_details":{"reasoning_tokens":0,"audio_tokens":0,"accepted_prediction_tokens":0,"rejected_prediction_tokens":0}}}

            data: [DONE]

        headers:
            Access-Control-Expose-Headers:
                - X-Request-ID
            Alt-Svc:
                - h3=":443"; ma=86400
            Cf-Cache-Status:
                - DYNAMIC
            Cf-Ray:
                - 96308674cb6ae580-KUL
            Content-Type:
                - text/event-stream; charset=utf-8
            Date:
                - Tue, 22 Jul 2025 05:26:48 GMT
            Openai-Processing-Ms:
                - "192"
            Openai-Project:
                - proj_KYenqYOfeZsnXEVK8dXVBhez
            Openai-Version:
                - "2020-10-01"
            Server:
                - cloudflare
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains; preload
            X-Content-Type-Options:
                - nosniff
            X-Envoy-Upstream-Service-Time:
                - "197"
            X-Ratelimit-Limit-Requests:
                - "500"
            X-Ratelimit-Limit-Tokens:
                - "200000"
            X-Ratelimit-Remaining-Requests:
                - "499"
            X-Ratelimit-Remaining-Tokens:
                - "199988"
            X-Ratelimit-Reset-Requests:
                - 120ms
            X-Ratelimit-Reset-Tokens:
                - 3ms
            X-Request-Id:
                - req_ac7069593f26ebc41cf72b6f4713529a
        status: 200 OK
        code: 200
        duration: 613.113917ms
