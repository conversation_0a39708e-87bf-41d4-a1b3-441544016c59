---
version: 2
interactions:
    - id: 0
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 116
        host: api.openai.com
        body: |-
            {
              "messages": [
                {
                  "content": "Hello!",
                  "role": "user"
                }
              ],
              "model": "gpt-4.1-nano-wrong"
            }
        headers:
            Accept-Encoding:
                - gzip
            Content-Length:
                - "78"
            Content-Type:
                - application/json
            User-Agent:
                - Go-http-client/1.1
        url: https://api.openai.com/v1/chat/completions
        method: POST
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        content_length: 224
        body: |
            {
                "error": {
                    "message": "The model `gpt-4.1-nano-wrong` does not exist or you do not have access to it.",
                    "type": "invalid_request_error",
                    "param": null,
                    "code": "model_not_found"
                }
            }
        headers:
            Alt-Svc:
                - h3=":443"; ma=86400
            Cf-Cache-Status:
                - DYNAMIC
            Cf-Ray:
                - 963087d4289ae580-KUL
            Content-Type:
                - application/json; charset=utf-8
            Date:
                - Tue, 22 Jul 2025 05:27:44 GMT
            Server:
                - cloudflare
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains; preload
            Vary:
                - Origin
            X-Content-Type-Options:
                - nosniff
            X-Request-Id:
                - req_a282785522a2adc2c5b96a62bfeaacd1
        status: 404 Not Found
        code: 404
        duration: 775.834042ms
