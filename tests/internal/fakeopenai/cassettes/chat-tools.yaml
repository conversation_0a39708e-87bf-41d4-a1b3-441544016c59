---
version: 2
interactions:
    - id: 0
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 834
        host: api.openai.com
        body: |-
            {
              "messages": [
                {
                  "content": "What is the weather like in Boston today?",
                  "role": "user"
                }
              ],
              "model": "gpt-4.1-nano",
              "tool_choice": "auto",
              "tools": [
                {
                  "function": {
                    "description": "Get the current weather in a given location",
                    "name": "get_current_weather",
                    "parameters": {
                      "properties": {
                        "location": {
                          "description": "The city and state, e.g. San Francisco, CA",
                          "type": "string"
                        },
                        "unit": {
                          "enum": [
                            "celsius",
                            "fahrenheit"
                          ],
                          "type": "string"
                        }
                      },
                      "required": [
                        "location"
                      ],
                      "type": "object"
                    }
                  },
                  "type": "function"
                }
              ]
            }
        headers:
            Accept-Encoding:
                - gzip
            Content-Length:
                - "475"
            Content-Type:
                - application/json
            User-Agent:
                - Go-http-client/1.1
        url: https://api.openai.com/v1/chat/completions
        method: POST
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        content_length: 1337
        body: |-
            {
              "choices": [
                {
                  "finish_reason": "tool_calls",
                  "index": 0,
                  "logprobs": null,
                  "message": {
                    "annotations": [],
                    "content": null,
                    "refusal": null,
                    "role": "assistant",
                    "tool_calls": [
                      {
                        "function": {
                          "arguments": "{\"location\": \"Boston\"}",
                          "name": "get_current_weather"
                        },
                        "id": "call_26LsX7EjeYJZFKQVtq7cZKmC",
                        "type": "function"
                      },
                      {
                        "function": {
                          "arguments": "{\"location\": \"Boston\"}",
                          "name": "get_current_weather"
                        },
                        "id": "call_pQt0Pxu6mOA30pUBGfiTTgn1",
                        "type": "function"
                      }
                    ]
                  }
                }
              ],
              "created": 1753162009,
              "id": "chatcmpl-BvzoXS47ObBgHCv5ypPuJjnUQcSk9",
              "model": "gpt-4.1-nano-2025-04-14",
              "object": "chat.completion",
              "service_tier": "default",
              "system_fingerprint": null,
              "usage": {
                "completion_tokens": 46,
                "completion_tokens_details": {
                  "accepted_prediction_tokens": 0,
                  "audio_tokens": 0,
                  "reasoning_tokens": 0,
                  "rejected_prediction_tokens": 0
                },
                "prompt_tokens": 81,
                "prompt_tokens_details": {
                  "audio_tokens": 0,
                  "cached_tokens": 0
                },
                "total_tokens": 127
              }
            }
        headers:
            Access-Control-Expose-Headers:
                - X-Request-ID
            Alt-Svc:
                - h3=":443"; ma=86400
            Cf-Cache-Status:
                - DYNAMIC
            Cf-Ray:
                - 9630867decb1e580-KUL
            Content-Type:
                - application/json
            Date:
                - Tue, 22 Jul 2025 05:26:50 GMT
            Openai-Processing-Ms:
                - "505"
            Openai-Project:
                - proj_KYenqYOfeZsnXEVK8dXVBhez
            Openai-Version:
                - "2020-10-01"
            Server:
                - cloudflare
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains; preload
            X-Content-Type-Options:
                - nosniff
            X-Envoy-Upstream-Service-Time:
                - "509"
            X-Ratelimit-Limit-Requests:
                - "500"
            X-Ratelimit-Limit-Tokens:
                - "200000"
            X-Ratelimit-Remaining-Requests:
                - "499"
            X-Ratelimit-Remaining-Tokens:
                - "199987"
            X-Ratelimit-Reset-Requests:
                - 120ms
            X-Ratelimit-Reset-Tokens:
                - 3ms
            X-Request-Id:
                - 7a022348-51cc-4df0-a08e-043039f28375
        status: 200 OK
        code: 200
        duration: 934.572125ms
