---
version: 2
interactions:
    - id: 0
      request:
        proto: HTTP/1.1
        proto_major: 1
        proto_minor: 1
        content_length: 393
        host: api.openai.com
        body: |-
            {
              "messages": [
                {
                  "content": "You are a helpful assistant.",
                  "role": "developer"
                },
                {
                  "content": "Hello!",
                  "role": "user"
                },
                {
                  "content": "Hello! How can I assist you today?",
                  "role": "assistant"
                },
                {
                  "content": "What's the weather like?",
                  "role": "user"
                }
              ],
              "model": "gpt-4.1-nano",
              "temperature": 0.7
            }
        headers:
            Accept-Encoding:
                - gzip
            Content-Length:
                - "273"
            Content-Type:
                - application/json
            User-Agent:
                - Go-http-client/1.1
        url: https://api.openai.com/v1/chat/completions
        method: POST
      response:
        proto: HTTP/2.0
        proto_major: 2
        proto_minor: 0
        content_length: 1038
        body: |-
            {
              "choices": [
                {
                  "finish_reason": "stop",
                  "index": 0,
                  "logprobs": null,
                  "message": {
                    "annotations": [],
                    "content": "I don't have real-time access to current weather data. However, I can help you find the weather forecast if you tell me your location. Alternatively, you can check a weather website or app for the latest updates. How can I assist you further?",
                    "refusal": null,
                    "role": "assistant"
                  }
                }
              ],
              "created": 1753162021,
              "id": "chatcmpl-BvzojqxiCJvlfUn7EkHrdDbGaNqwg",
              "model": "gpt-4.1-nano-2025-04-14",
              "object": "chat.completion",
              "service_tier": "default",
              "system_fingerprint": null,
              "usage": {
                "completion_tokens": 50,
                "completion_tokens_details": {
                  "accepted_prediction_tokens": 0,
                  "audio_tokens": 0,
                  "reasoning_tokens": 0,
                  "rejected_prediction_tokens": 0
                },
                "prompt_tokens": 41,
                "prompt_tokens_details": {
                  "audio_tokens": 0,
                  "cached_tokens": 0
                },
                "total_tokens": 91
              }
            }
        headers:
            Access-Control-Expose-Headers:
                - X-Request-ID
            Alt-Svc:
                - h3=":443"; ma=86400
            Cf-Cache-Status:
                - DYNAMIC
            Cf-Ray:
                - 963086b63e3ee580-KUL
            Content-Type:
                - application/json
            Date:
                - Tue, 22 Jul 2025 05:27:17 GMT
            Openai-Processing-Ms:
                - "16147"
            Openai-Project:
                - proj_KYenqYOfeZsnXEVK8dXVBhez
            Openai-Version:
                - "2020-10-01"
            Server:
                - cloudflare
            Strict-Transport-Security:
                - max-age=31536000; includeSubDomains; preload
            X-Content-Type-Options:
                - nosniff
            X-Envoy-Upstream-Service-Time:
                - "16152"
            X-Ratelimit-Limit-Requests:
                - "500"
            X-Ratelimit-Limit-Tokens:
                - "200000"
            X-Ratelimit-Remaining-Requests:
                - "499"
            X-Ratelimit-Remaining-Tokens:
                - "199972"
            X-Ratelimit-Reset-Requests:
                - 120ms
            X-Ratelimit-Reset-Tokens:
                - 8ms
            X-Request-Id:
                - req_38d4f47e4f8bda9ff1652ede5f136b92
        status: 200 OK
        code: 200
        duration: 19.396106583s
