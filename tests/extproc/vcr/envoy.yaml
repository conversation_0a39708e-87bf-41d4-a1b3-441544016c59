# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

static_resources:
  listeners:
    - name: listener_0
      address:
        socket_address:
          address: 0.0.0.0
          port_value: 1062
      filter_chains:
        - filters:
            - name: envoy.filters.network.http_connection_manager
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                stat_prefix: ingress_http
                # Add access log for test inspection
                access_log:
                  - name: envoy.access_loggers.stdout
                    typed_config:
                      "@type": type.googleapis.com/envoy.extensions.access_loggers.stream.v3.StdoutAccessLog
                route_config:
                  name: local_route
                  virtual_hosts:
                    - name: backend
                      domains: ["*"]
                      routes:
                        # These paths must match those registered in cmd/extproc/mainlib/main.go
                        - match:
                            path: "/v1/chat/completions"
                          route:
                            cluster: openai
                        - match:
                            path: "/v1/embeddings"
                          route:
                            cluster: openai
                        - match:
                            path: "/v1/models"
                          route:
                            cluster: openai
                        - match:
                            prefix: "/"
                          direct_response:
                            status: 404
                            body:
                              inline_string: 'not forwarding paths except in cmd/extproc/mainlib/main.go'
                          typed_per_filter_config:
                            envoy.filters.http.ext_proc:
                              "@type": type.googleapis.com/envoy.config.route.v3.FilterConfig
                              disabled: true
                http_filters:
                  # Simulate real config injected via EnvoyExtensionPolicy
                  - name: envoy.filters.http.ext_proc
                    typed_config:
                      "@type": type.googleapis.com/envoy.extensions.filters.http.ext_proc.v3.ExternalProcessor
                      allow_mode_override: true
                      grpc_service:
                        envoy_grpc:
                          cluster_name: ext_proc
                      processing_mode:
                        request_header_mode: SEND
                        response_header_mode: SEND
                        request_body_mode: BUFFERED
                        response_body_mode: BUFFERED
                      metadataOptions:
                        receivingNamespaces:
                          untyped:
                            - ai_gateway_llm_ns
                  - name: envoy.filters.http.router
                    typed_config:
                      "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
  clusters:
    - name: openai
      connect_timeout: 0.25s
      type: STRICT_DNS
      lb_policy: ROUND_ROBIN
      load_assignment:
        cluster_name: openai
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: host.docker.internal
                      port_value: 11434
                # Add metadata in prod set by XDS: xds.upstream_host_metadata
                metadata:
                  filter_metadata:
                    aigateway.envoy.io:
                      per_route_rule_backend_name: "openai"
    - name: ext_proc
      connect_timeout: 0.25s
      type: STRICT_DNS
      lb_policy: ROUND_ROBIN
      load_assignment:
        cluster_name: ext_proc
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: extproc
                      port_value: 1063
      typed_extension_protocol_options:
        envoy.extensions.upstreams.http.v3.HttpProtocolOptions:
          "@type": type.googleapis.com/envoy.extensions.upstreams.http.v3.HttpProtocolOptions
          explicit_http_config:
            http2_protocol_options: {}

